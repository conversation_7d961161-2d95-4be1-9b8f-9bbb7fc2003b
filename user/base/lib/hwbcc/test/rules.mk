# Copyright (C) 2021 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

LOCAL_DIR := $(GET_LOCAL_DIR)
LIBCPPBOR_DIR := $(if $(wildcard system/libcppbor),system/libcppbor,external/libcppbor)

MODULE := $(LOCAL_DIR)

MANIFEST := $(LOCAL_DIR)/manifest.json

MODULE_SRCS := \
	$(LOCAL_DIR)/main.cpp \

MODULE_LIBRARY_DEPS := \
	external/Tongsuo \
	$(LIBCPPBOR_DIR) \
	external/open-dice \
	trusty/user/base/lib/hwbcc/client \
	trusty/user/base/lib/hwbcc/common \
	trusty/user/base/lib/libc-rctee \
	trusty/user/base/lib/libstdc++-trusty \
	trusty/user/base/lib/system_state \
	trusty/user/base/lib/unittest \

include make/trusted_app.mk
