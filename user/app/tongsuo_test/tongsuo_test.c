/*
 * Copyright (C) 2023 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/sha.h>
#include <openssl/aes.h>
#include <openssl/rsa.h>
#include <openssl/ec.h>
#include <openssl/sm2.h>
#include <openssl/sm3.h>
#include <openssl/sm4.h>

#include <rctee_app.h>
#include <tipc.h>

#define TONGSUO_TEST_PORT "com.android.trusty.tongsuo_test"

static void test_sha256(void) {
    printf("Testing SHA256...\n");
    
    const char* data = "Hello, Tongsuo!";
    unsigned char hash[SHA256_DIGEST_LENGTH];
    
    SHA256_CTX ctx;
    SHA256_Init(&ctx);
    SHA256_Update(&ctx, data, strlen(data));
    SHA256_Final(hash, &ctx);
    
    printf("SHA256 test passed\n");
}

static void test_aes(void) {
    printf("Testing AES...\n");
    
    unsigned char key[16] = {0};
    unsigned char iv[16] = {0};
    unsigned char plaintext[16] = "Hello, AES!";
    unsigned char ciphertext[32];
    unsigned char decrypted[32];
    int len;
    
    EVP_CIPHER_CTX *ctx = EVP_CIPHER_CTX_new();
    
    // Encrypt
    EVP_EncryptInit_ex(ctx, EVP_aes_128_cbc(), NULL, key, iv);
    EVP_EncryptUpdate(ctx, ciphertext, &len, plaintext, strlen((char*)plaintext));
    EVP_EncryptFinal_ex(ctx, ciphertext + len, &len);
    
    // Decrypt
    EVP_DecryptInit_ex(ctx, EVP_aes_128_cbc(), NULL, key, iv);
    EVP_DecryptUpdate(ctx, decrypted, &len, ciphertext, 16);
    EVP_DecryptFinal_ex(ctx, decrypted + len, &len);
    
    EVP_CIPHER_CTX_free(ctx);
    
    printf("AES test passed\n");
}

static void test_sm3(void) {
    printf("Testing SM3...\n");
    
    const char* data = "Hello, SM3!";
    unsigned char hash[SM3_DIGEST_LENGTH];
    
    SM3_CTX ctx;
    SM3_Init(&ctx);
    SM3_Update(&ctx, data, strlen(data));
    SM3_Final(hash, &ctx);
    
    printf("SM3 test passed\n");
}

static void test_sm4(void) {
    printf("Testing SM4...\n");
    
    unsigned char key[16] = {0};
    unsigned char iv[16] = {0};
    unsigned char plaintext[16] = "Hello, SM4!";
    unsigned char ciphertext[32];
    unsigned char decrypted[32];
    int len;
    
    EVP_CIPHER_CTX *ctx = EVP_CIPHER_CTX_new();
    
    // Encrypt
    EVP_EncryptInit_ex(ctx, EVP_sm4_cbc(), NULL, key, iv);
    EVP_EncryptUpdate(ctx, ciphertext, &len, plaintext, strlen((char*)plaintext));
    EVP_EncryptFinal_ex(ctx, ciphertext + len, &len);
    
    // Decrypt
    EVP_DecryptInit_ex(ctx, EVP_sm4_cbc(), NULL, key, iv);
    EVP_DecryptUpdate(ctx, decrypted, &len, ciphertext, 16);
    EVP_DecryptFinal_ex(ctx, decrypted + len, &len);
    
    EVP_CIPHER_CTX_free(ctx);
    
    printf("SM4 test passed\n");
}

static void run_tongsuo_tests(void) {
    printf("Starting Tongsuo crypto tests...\n");
    
    test_sha256();
    test_aes();
    test_sm3();
    test_sm4();
    
    printf("All Tongsuo tests completed successfully!\n");
}

static int handle_msg(handle_t chan) {
    int rc;
    struct rctee_ipc_msg_info msg_inf;
    
    rc = get_msg(chan, &msg_inf);
    if (rc != NO_ERROR) {
        printf("Failed to get message: %d\n", rc);
        return rc;
    }
    
    if (msg_inf.len > 0) {
        char buf[256];
        struct iovec iov = {buf, sizeof(buf)};
        struct rctee_ipc_msg msg = {1, &iov, 0, NULL};
        
        rc = read_msg(chan, msg_inf.id, 0, &msg);
        if (rc < 0) {
            printf("Failed to read message: %d\n", rc);
            put_msg(chan, msg_inf.id);
            return rc;
        }
        
        printf("Received message: %.*s\n", rc, buf);
        
        // Run tests when requested
        if (strncmp(buf, "test", 4) == 0) {
            run_tongsuo_tests();
        }
    }
    
    put_msg(chan, msg_inf.id);
    return NO_ERROR;
}

int main(void) {
    int rc;
    handle_t port;
    
    printf("Tongsuo test application starting...\n");
    
    // Run initial tests
    run_tongsuo_tests();
    
    // Create port for IPC
    rc = port_create(TONGSUO_TEST_PORT, 1, 256, IPC_PORT_ALLOW_NS_CONNECT);
    if (rc < 0) {
        printf("Failed to create port: %d\n", rc);
        return rc;
    }
    port = (handle_t)rc;
    
    printf("Tongsuo test service ready on port: %s\n", TONGSUO_TEST_PORT);
    
    // Main event loop
    while (true) {
        rctee_event_t event;
        rc = wait(&event, INFINITE_TIME);
        if (rc != NO_ERROR) {
            printf("Wait failed: %d\n", rc);
            break;
        }
        
        if (event.handle == port) {
            if (event.event & IPC_HANDLE_POLL_READY) {
                handle_t chan;
                rc = accept(port, &chan);
                if (rc >= 0) {
                    printf("New connection accepted\n");
                    close(chan);
                }
            }
        } else {
            if (event.event & IPC_HANDLE_POLL_MSG) {
                rc = handle_msg(event.handle);
                if (rc != NO_ERROR) {
                    printf("Message handling failed: %d\n", rc);
                }
            }
            if (event.event & IPC_HANDLE_POLL_HUP) {
                close(event.handle);
            }
        }
    }
    
    close(port);
    return 0;
}
