#
# Copyright (C) 2020 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

LOCAL_DIR := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

MANIFEST := $(LOCAL_DIR)/manifest.json

MODULE_CONSTANTS := $(LOCAL_DIR)/hwaes_consts.json

MODULE_INCLUDES := $(LOCAL_DIR)/include

MODULE_SRCS := \
	$(LOCAL_DIR)/main.c \

MODULE_INCLUDES += \
	trusty/user/app/sample/hwcrypto/include \

MODULE_LIBRARY_DEPS := \
	trusty/user/base/lib/libc-rctee \
	trusty/user/base/lib/hwaes/srv \
	trusty/user/base/lib/hwkey \
	trusty/user/base/lib/tipc \
	external/Tongsuo \

include make/trusted_app.mk
