# Tongsuo 自动集成指南

本文档说明如何在 trusty-tee 项目中自动集成 Tongsuo 密码学库，实现类似 BoringSSL 的无缝集成体验。

## 概述

Tongsuo 已经完全集成到 trusty-tee 构建系统中，支持自动发现和链接，无需手动配置。

## 自动集成机制

### 1. 构建系统自动设置

构建系统会在构建过程中自动创建必要的符号链接：

```bash
# 构建时会自动执行
make setup-external-libs
```

这会自动创建：
- `kernel/lk/external/Tongsuo` -> `../../../opensource_libs/Tongsuo`

### 2. 手动设置（可选）

如果需要手动设置外部库链接，可以运行：

```bash
./setup_external_libs.sh
```

## 使用方法

### 在模块中使用 Tongsuo

在您的 `rules.mk` 文件中添加依赖：

```makefile
LOCAL_DIR := $(GET_LOCAL_DIR)
MODULE := $(LOCAL_DIR)

MODULE_SRCS := $(LOCAL_DIR)/your_source.c

# 添加 Tongsuo 依赖
MODULE_LIBRARY_DEPS += external/Tongsuo

include make/library.mk
```

### 包含头文件

```c
#include <openssl/ssl.h>
#include <openssl/crypto.h>
#include <openssl/sm2.h>
#include <openssl/sm3.h>
#include <openssl/sm4.h>
```

## 构建配置

### Tongsuo 特定配置

Tongsuo 在 Trusty 环境中使用以下配置：

- `OPENSSL_NO_ASM`: 禁用汇编优化
- `OPENSSL_SMALL`: 小型化构建
- `OPENSSL_NO_THREADS_CORRUPT_MEMORY_AND_LEAK_SECRETS_IF_THREADED`: 线程安全配置
- `__STDC_NO_ATOMICS__`: 禁用 C11 原子操作

### 架构支持

支持的架构特性：
- ARM NEON 指令集
- ARM AES 硬件加速
- ARM PMULL 指令
- ARM SHA1/SHA256 硬件加速

## 与 BoringSSL 的区别

### 相同点
- 自动符号链接创建
- 构建系统自动发现
- 模块依赖声明方式相同

### 不同点
- Tongsuo 支持国密算法（SM2/SM3/SM4）
- 不同的编译配置选项
- 专门的 Trusty 配置文件

## 验证集成

### 检查符号链接
```bash
ls -la kernel/lk/external/Tongsuo
# 应该显示: Tongsuo -> ../../../opensource_libs/Tongsuo
```

### 检查构建配置
```bash
ls kernel/lk/external/Tongsuo/rules.mk
# 应该存在且可读
```

### 测试构建
```bash
# 使用您的构建脚本
./local_build.sh

# 或直接使用 make
make imx8mp
```

## 故障排除

### 符号链接不存在
运行设置脚本：
```bash
./setup_external_libs.sh
```

### 构建错误
1. 确认 `opensource_libs/Tongsuo` 目录存在
2. 检查符号链接是否正确
3. 验证 `rules.mk` 文件存在

### 模块未找到错误
确保在 `MODULE_LIBRARY_DEPS` 中使用正确的路径：
```makefile
MODULE_LIBRARY_DEPS += external/Tongsuo  # 正确
# 不要使用: opensource_libs/Tongsuo
```

## 高级配置

### 自定义编译选项

如需自定义 Tongsuo 编译选项，编辑 `kernel/lk/external/Tongsuo/rules.mk`：

```makefile
# 添加自定义标志
MODULE_CFLAGS += -DCUSTOM_TONGSUO_FLAG
```

### 条件编译

可以基于构建配置条件性地包含 Tongsuo：

```makefile
ifeq ($(USE_TONGSUO),true)
MODULE_LIBRARY_DEPS += external/Tongsuo
MODULE_CFLAGS += -DUSE_TONGSUO=1
endif
```

## 总结

Tongsuo 现在完全集成到 trusty-tee 构建系统中，提供：

1. **自动设置**：构建时自动创建符号链接
2. **无缝集成**：与 BoringSSL 相同的使用方式
3. **国密支持**：完整的 SM2/SM3/SM4 算法支持
4. **Trusty 优化**：专门针对 Trusty 环境的配置

只需在 `MODULE_LIBRARY_DEPS` 中添加 `external/Tongsuo`，即可开始使用 Tongsuo 的所有功能。
