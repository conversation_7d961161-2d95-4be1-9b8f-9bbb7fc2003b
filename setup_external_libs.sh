#!/bin/bash

# Setup script for external library symlinks in trusty-tee
# This script creates the necessary symlinks for external libraries
# to be automatically discovered by the Trusty build system

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
EXTERNAL_DIR="$SCRIPT_DIR/kernel/lk/external"

echo "Setting up external library symlinks for trusty-tee..."

# Create external directory if it doesn't exist
mkdir -p "$EXTERNAL_DIR"

# Setup Tongsuo symlink
TONGSUO_LINK="$EXTERNAL_DIR/Tongsuo"
TONGSUO_TARGET="../../../opensource_libs/Tongsuo"

if [ -L "$TONGSUO_LINK" ]; then
    echo "✓ Tongsuo symlink already exists"
elif [ -e "$TONGSUO_LINK" ]; then
    echo "✗ Error: $TONGSUO_LINK exists but is not a symlink"
    exit 1
else
    echo "Creating Tongsuo symlink..."
    ln -sf "$TONGSUO_TARGET" "$TONGSUO_LINK"
    echo "✓ Created Tongsuo symlink: $TONGSUO_LINK -> $TONGSUO_TARGET"
fi

# Verify the symlink works
if [ -f "$TONGSUO_LINK/rules.mk" ]; then
    echo "✓ Tongsuo integration verified (rules.mk found)"
else
    echo "✗ Warning: Tongsuo rules.mk not found, symlink may be incorrect"
fi

echo ""
echo "External library setup complete!"
echo ""
echo "You can now use Tongsuo in your MODULE_LIBRARY_DEPS like this:"
echo "  MODULE_LIBRARY_DEPS += external/Tongsuo"
echo ""
echo "The build system will automatically discover and include Tongsuo"
echo "without requiring manual symlink creation."
